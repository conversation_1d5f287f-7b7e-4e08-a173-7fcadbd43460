# NST Dev Club Portal

A modern web application for managing NST Dev Club members, projects, meetings, and activities. Built with Next.js 15, NextAuth v5, and Supabase.

## Features

- 🔐 **Authentication**: OAuth integration with GitHub, Google, and LinkedIn via NextAuth v5
- 📊 **Dashboard**: Comprehensive overview of club activities and personal progress
- 👥 **Member Management**: View and manage club member profiles
- 🚀 **Project Tracking**: Create, manage, and track development projects
- 📅 **Meeting Management**: Schedule and track meeting attendance
- 🏆 **Reward System**: Point-based system for tracking contributions
- 🌙 **Dark Mode**: Full dark/light theme support
- 📱 **Responsive Design**: Mobile-first responsive design with Tailwind CSS

## Tech Stack

- **Frontend**: Next.js 15, React 19, Tailwind CSS
- **Authentication**: NextAuth v5 with Supabase adapter
- **Database**: Supabase (PostgreSQL)
- **Icons**: React Icons (Feather Icons)
- **Charts**: Chart.js with react-chartjs-2
- **State Management**: Zustand
- **HTTP Client**: Axios

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, pnpm, or bun
- Supabase account
- OAuth app credentials (GitHub, Google, LinkedIn)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd nst-dev-club-portal
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
# or
bun install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your actual values:
```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# OAuth Provider Credentials
GITHUB_ID=your-github-client-id
GITHUB_SECRET=your-github-client-secret

GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
```

4. Set up Supabase database:
   - Create a new Supabase project
   - Run the SQL commands from `supabase-schema.sql` in your Supabase SQL editor
   - This will create the necessary tables for NextAuth and user management

5. Configure OAuth providers:
   - **GitHub**: Create an OAuth app in GitHub Developer Settings
   - **Google**: Set up OAuth 2.0 credentials in Google Cloud Console
   - **LinkedIn**: Create an app in LinkedIn Developer Portal

6. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Authentication Setup

### Supabase Configuration

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Go to Settings > Database and copy the service role key
4. Run the SQL schema from `supabase-schema.sql` in the SQL editor

### OAuth Provider Setup

#### GitHub OAuth App
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Click "New OAuth App"
3. Set Authorization callback URL to: `http://localhost:3000/api/auth/callback/github`
4. Copy Client ID and Client Secret to your `.env.local`

#### Google OAuth 2.0
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to Credentials > Create Credentials > OAuth 2.0 Client IDs
5. Set Authorized redirect URI to: `http://localhost:3000/api/auth/callback/google`
6. Copy Client ID and Client Secret to your `.env.local`

#### LinkedIn OAuth App
1. Go to [LinkedIn Developer Portal](https://www.linkedin.com/developers/)
2. Create a new app
3. Add "Sign In with LinkedIn" product
4. Set Authorized redirect URL to: `http://localhost:3000/api/auth/callback/linkedin`
5. Copy Client ID and Client Secret to your `.env.local`

## Project Structure

```
src/
├── app/                    # Next.js 13+ app directory
│   ├── api/auth/          # NextAuth API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard page
│   ├── projects/          # Project management pages
│   ├── students/          # Member management pages
│   ├── meetings/          # Meeting management pages
│   └── layout.js          # Root layout with SessionProvider
├── components/            # Reusable React components
│   ├── Navigation.js      # Main navigation component
│   └── SessionProvider.js # NextAuth session provider wrapper
└── lib/                   # Utility libraries
    └── supabase.js        # Supabase client configuration
```

## Database Schema

The application uses Supabase (PostgreSQL) with the following main tables:

- `users` - User profiles and metadata
- `accounts` - OAuth account connections
- `sessions` - User sessions
- `verification_tokens` - Email verification tokens

See `supabase-schema.sql` for the complete schema.

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue on GitHub or contact the development team.

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Validate environment variables
const isValidUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Only create clients if we have valid environment variables
let supabase = null;
let supabaseAdmin = null;

if (supabaseUrl && supabaseAnonKey && isValidUrl(supabaseUrl)) {
  // Client-side Supabase client
  supabase = createClient(supabaseUrl, supabaseAnonKey)
}

if (supabaseUrl && supabaseServiceRoleKey && isValidUrl(supabaseUrl)) {
  // Server-side Supabase client with service role key (for admin operations)
  supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Helper function to create a Supabase client for server-side operations
export function createServerSupabaseClient() {
  if (!supabaseUrl || !supabaseServiceRoleKey || !isValidUrl(supabaseUrl)) {
    console.warn('Supabase environment variables not configured properly');
    return null;
  }

  return createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export { supabase, supabaseAdmin }

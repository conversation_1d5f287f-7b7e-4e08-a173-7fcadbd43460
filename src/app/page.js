"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>C<PERSON>, FiUsers, FiAward, FiCalendar, FiArrowRight } from "react-icons/fi";

export default function Home() {
  const router = useRouter();
  const { user, loading } = useAuth();

  useEffect(() => {
    // Redirect to dashboard if authenticated
    if (user) {
      router.push('/dashboard');
    }
  }, [user, router]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If not authenticated or while waiting for redirect, show landing page
  if (!user) {
  return (
    <div className="flex flex-col gap-16">
      {/* Futuristic Hero Section */}
      <section className="relative z-10 matrix-bg text-center py-20 px-4 sm:px-6 lg:px-8 min-h-screen flex items-center justify-center">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-6xl md:text-8xl font-bold mb-8">
            <span className="block holographic">WELCOME TO</span>
            <span className="block neon-blue text-7xl md:text-9xl font-black tracking-wider">
              NST DEV CLUB
            </span>
          </h1>

          <div className="typing-effect mx-auto max-w-3xl mb-12">
            <p className="text-xl md:text-2xl text-gray-300 font-mono">
              &gt; Initializing developer community...
            </p>
          </div>

          <p className="text-lg md:text-xl text-gray-400 mb-12 max-w-4xl mx-auto leading-relaxed">
            Join our <span className="neon-purple">elite community</span> of passionate developers.
            Learn <span className="neon-green">cutting-edge technologies</span>, build
            <span className="neon-pink"> amazing projects</span>, and level up your skills
            in our <span className="neon-blue">futuristic environment</span>.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link
              href="/auth/signin"
              className="btn-primary px-12 py-4 text-lg font-bold pulse-glow"
            >
              <span className="flex items-center">
                🚀 ENTER THE MATRIX
              </span>
            </Link>
            <Link
              href="/projects"
              className="btn-secondary px-12 py-4 text-lg font-bold"
            >
              <span className="flex items-center">
                🔮 EXPLORE PROJECTS
              </span>
            </Link>
          </div>
        </div>
      </section>

      {/* Futuristic Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-5xl font-bold text-center mb-4 holographic">SYSTEM FEATURES</h2>
          <p className="text-center text-gray-400 mb-16 font-mono">&gt; Advanced capabilities unlocked</p>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="card scan-lines flex flex-col items-center text-center p-8 group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full blur-lg opacity-75 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-black p-4 rounded-full border border-cyan-500">
                  <FiCode className="h-8 w-8 neon-blue" />
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 neon-blue">PROJECT MATRIX</h3>
              <p className="text-gray-400 leading-relaxed">
                Deploy and showcase your <span className="neon-green">cutting-edge projects</span> in our digital ecosystem.
              </p>
            </div>

            <div className="card scan-lines flex flex-col items-center text-center p-8 group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-lg opacity-75 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-black p-4 rounded-full border border-purple-500">
                  <FiUsers className="h-8 w-8 neon-purple" />
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 neon-purple">NEURAL NETWORK</h3>
              <p className="text-gray-400 leading-relaxed">
                Connect with <span className="neon-purple">elite developers</span> and access their digital profiles.
              </p>
            </div>

            <div className="card scan-lines flex flex-col items-center text-center p-8 group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full blur-lg opacity-75 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-black p-4 rounded-full border border-green-500">
                  <FiAward className="h-8 w-8 neon-green" />
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 neon-green">QUANTUM REWARDS</h3>
              <p className="text-gray-400 leading-relaxed">
                Earn <span className="neon-green">digital currency</span> for contributions and achievements.
              </p>
            </div>

            <div className="card scan-lines flex flex-col items-center text-center p-8 group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-full blur-lg opacity-75 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-black p-4 rounded-full border border-orange-500">
                  <FiCalendar className="h-8 w-8 text-orange-400" />
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 text-orange-400">TIME SYNC</h3>
              <p className="text-gray-400 leading-relaxed">
                Schedule <span className="text-orange-400">virtual meetings</span> and track participation metrics.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Futuristic Stats Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 cyber-grid">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-5xl font-bold text-center mb-4 holographic">SYSTEM METRICS</h2>
          <p className="text-center text-gray-400 mb-16 font-mono">&gt; Real-time data stream</p>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="card text-center p-8 pulse-glow">
              <div className="text-6xl font-black neon-blue mb-4 font-mono">25+</div>
              <div className="text-gray-400 uppercase tracking-wider font-bold">
                <span className="neon-blue">ACTIVE</span><br/>PROJECTS
              </div>
            </div>

            <div className="card text-center p-8 pulse-glow">
              <div className="text-6xl font-black neon-purple mb-4 font-mono">100+</div>
              <div className="text-gray-400 uppercase tracking-wider font-bold">
                <span className="neon-purple">NEURAL</span><br/>NODES
              </div>
            </div>

            <div className="card text-center p-8 pulse-glow">
              <div className="text-6xl font-black neon-green mb-4 font-mono">500+</div>
              <div className="text-gray-400 uppercase tracking-wider font-bold">
                <span className="neon-green">CODE</span><br/>COMMITS
              </div>
            </div>

            <div className="card text-center p-8 pulse-glow">
              <div className="text-6xl font-black neon-pink mb-4 font-mono">50+</div>
              <div className="text-gray-400 uppercase tracking-wider font-bold">
                <span className="neon-pink">SYNC</span><br/>SESSIONS
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Futuristic CTA Section */}
      <section className="py-20 text-center relative">
        <div className="absolute inset-0 bg-gradient-to-r from-cyan-900/20 to-purple-900/20 rounded-lg"></div>
        <div className="relative z-10">
          <h2 className="text-5xl font-bold mb-6 holographic">READY TO JACK IN?</h2>
          <p className="text-xl max-w-4xl mx-auto mb-12 text-gray-400 leading-relaxed">
            <span className="font-mono">&gt;</span> Initialize your connection to the
            <span className="neon-blue"> NST Dev Club Matrix</span>.
            Join the <span className="neon-purple">neural network</span> of elite developers
            and unlock your <span className="neon-green">digital potential</span>.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link
              href="/auth/signin"
              className="btn-primary px-16 py-6 text-xl font-bold pulse-glow"
            >
              <span className="flex items-center">
                ⚡ CONNECT NOW <FiArrowRight className="ml-3" />
              </span>
            </Link>
          </div>
          <p className="mt-8 text-gray-500 font-mono text-sm">
            &gt; Connection secured • Encryption enabled • Welcome to the future
          </p>
        </div>
      </section>
    </div>
  );
  }
  
  return null; // This will not be reached as we either show loading, redirect to dashboard, or show landing page
}

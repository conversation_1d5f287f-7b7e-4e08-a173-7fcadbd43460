import NextAuth from "next-auth";
import Gith<PERSON><PERSON>rovider from "next-auth/providers/github";
import GoogleProvider from "next-auth/providers/google";
import LinkedInProvider from "next-auth/providers/linkedin";
import { SupabaseAdapter } from "@auth/supabase-adapter";
import { createServerSupabaseClient } from "@/lib/supabase";

// Check if Supabase is properly configured
const isSupabaseConfigured = () => {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY;
  return url && key && url !== 'your-supabase-project-url' && key !== 'your-supabase-service-role-key';
};

export const authOptions = {
  // Only use Supabase adapter if properly configured, otherwise use JWT
  ...(isSupabaseConfigured() ? {
    adapter: SupabaseAdapter({
      url: process.env.NEXT_PUBLIC_SUPABASE_URL,
      secret: process.env.SUPABASE_SERVICE_ROLE_KEY,
    }),
    session: {
      strategy: "database",
    },
  } : {
    session: {
      strategy: "jwt",
    },
  }),
  providers: [
    GithubProvider({
      clientId: process.env.GITHUB_ID || "dummy",
      clientSecret: process.env.GITHUB_SECRET || "dummy",
      profile(profile) {
        return {
          id: profile.id.toString(),
          name: profile.name || profile.login,
          email: profile.email,
          image: profile.avatar_url,
          username: profile.login,
        };
      },
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || "dummy",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "dummy",
    }),
    LinkedInProvider({
      clientId: process.env.LINKEDIN_CLIENT_ID || "dummy",
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET || "dummy",
    }),
  ],
  callbacks: {
    async session({ session, user, token }) {
      if (isSupabaseConfigured() && user) {
        session.user.id = user.id;
        session.user.username = user.username;
      } else if (token) {
        session.user.id = token.sub;
        session.user.username = token.username;
      }
      return session;
    },
    async jwt({ token, user, account, profile }) {
      if (user) {
        token.username = user.username;
      }
      return token;
    },
    async signIn({ user, account, profile }) {
      // Store additional user data in Supabase only if configured
      if (isSupabaseConfigured() && account?.provider === "github" && profile) {
        const supabase = createServerSupabaseClient();

        if (supabase) {
          try {
            // Update user with GitHub username
            await supabase
              .from("users")
              .update({ username: profile.login })
              .eq("id", user.id);
          } catch (error) {
            console.warn("Failed to update user in Supabase:", error);
          }
        }
      }
      return true;
    },
  },
  pages: {
    signIn: "/auth/signin",
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };

@import "tailwindcss";

/* Futuristic CSS Variables */
:root {
  --primary: #00f5ff; /* Cyber Blue */
  --primary-light: #33f7ff;
  --primary-dark: #0099cc;
  --cyber-blue: #00f5ff;
  --cyber-purple: #8b5cf6;
  --cyber-pink: #ec4899;
  --cyber-green: #10b981;
  --cyber-orange: #f59e0b;
  --neon-glow: 0 0 20px currentColor;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --background: #0a0a0a;
  --foreground: #ffffff;
  --card-bg: rgba(255, 255, 255, 0.05);
  --border-color: rgba(0, 245, 255, 0.3);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Dark theme cyber colors */
.dark {
  --glass-bg: rgba(0, 0, 0, 0.3);
  --glass-border: rgba(255, 255, 255, 0.1);
  --background: #0a0a0a;
  --foreground: #ffffff;
  --card-bg: rgba(255, 255, 255, 0.05);
  --border-color: rgba(0, 245, 255, 0.3);
}

/* Futuristic Background */
body {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: var(--foreground);
  font-family: var(--font-sans), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Animated Background Particles */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, var(--cyber-blue), transparent),
    radial-gradient(2px 2px at 40px 70px, var(--cyber-purple), transparent),
    radial-gradient(1px 1px at 90px 40px, var(--cyber-pink), transparent),
    radial-gradient(1px 1px at 130px 80px, var(--cyber-green), transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: float 20s ease-in-out infinite;
  opacity: 0.3;
  z-index: -1;
}

/* Floating animation for background */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(-10px) rotate(-1deg); }
}

/* Glassmorphism Card Component */
.card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(0, 245, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Futuristic Buttons */
.btn-primary {
  background: linear-gradient(45deg, var(--cyber-blue), var(--cyber-purple));
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 0.75rem 1.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 245, 255, 0.3);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 245, 255, 0.5);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--cyber-blue);
  border-radius: 8px;
  color: var(--cyber-blue);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: var(--cyber-blue);
  color: white;
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
  transform: translateY(-2px);
}

/* Neon Glow Effects */
.neon-blue {
  color: var(--cyber-blue);
  text-shadow: 0 0 10px var(--cyber-blue);
}

.neon-purple {
  color: var(--cyber-purple);
  text-shadow: 0 0 10px var(--cyber-purple);
}

.neon-pink {
  color: var(--cyber-pink);
  text-shadow: 0 0 10px var(--cyber-pink);
}

.neon-green {
  color: var(--cyber-green);
  text-shadow: 0 0 10px var(--cyber-green);
}

/* Holographic Text Effect */
.holographic {
  background: linear-gradient(45deg, var(--cyber-blue), var(--cyber-purple), var(--cyber-pink));
  background-size: 300% 300%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: hologram 3s ease-in-out infinite;
}

@keyframes hologram {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Pulse Animation */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from { box-shadow: 0 0 20px rgba(0, 245, 255, 0.3); }
  to { box-shadow: 0 0 30px rgba(0, 245, 255, 0.6); }
}

/* Typing Effect */
.typing-effect {
  overflow: hidden;
  border-right: 2px solid var(--cyber-blue);
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--cyber-blue); }
}

/* Scan Line Effect */
.scan-lines::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    transparent 50%,
    rgba(0, 245, 255, 0.03) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
}

/* Matrix Rain Effect (subtle) */
.matrix-bg {
  position: relative;
  overflow: hidden;
}

.matrix-bg::before {
  content: '01001001 01001110 01001110 01001111 01010110 01000001 01010100 01001001 01001111 01001110';
  position: absolute;
  top: -100%;
  left: 0;
  width: 100%;
  height: 200%;
  color: var(--cyber-green);
  opacity: 0.1;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 20px;
  animation: matrix-fall 10s linear infinite;
  z-index: -1;
}

@keyframes matrix-fall {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

/* Futuristic Input Fields */
input, textarea {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(0, 245, 255, 0.3) !important;
  border-radius: 8px !important;
  color: white !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

input:focus, textarea:focus {
  border-color: var(--cyber-blue) !important;
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.3) !important;
  outline: none !important;
}

input::placeholder, textarea::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

/* Cyber Grid Pattern */
.cyber-grid {
  background-image:
    linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Loading Spinner */
.cyber-spinner {
  border: 2px solid rgba(0, 245, 255, 0.3);
  border-top: 2px solid var(--cyber-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

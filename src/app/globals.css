@import "tailwindcss";

:root {
  --primary: #2563eb; /* Blue */
  --primary-light: #3b82f6;
  --primary-dark: #1d4ed8;
  --background: #ffffff; /* White */
  --foreground: #171717;
  --card-bg: #f8fafc;
  --border-color: #e2e8f0;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --primary: #3b82f6; /* Blue */
    --primary-light: #60a5fa;
    --primary-dark: #2563eb;
    --background: #0f172a; /* Dark blue-black */
    --foreground: #f8fafc;
    --card-bg: #1e293b;
    --border-color: #334155;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
